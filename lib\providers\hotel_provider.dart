import 'package:flutter/material.dart';
import '../models/hotel.dart';

class HotelProvider extends ChangeNotifier {
  List<Hotel> _hotels = [];
  List<Hotel> _featuredHotels = [];
  List<Hotel> _searchResults = [];
  final List<Review> _hotelReviews = [];
  bool _isLoading = false;
  String _searchQuery = '';
  Hotel? _selectedHotel;

  // Getters
  List<Hotel> get hotels => _hotels;
  List<Hotel> get featuredHotels => _featuredHotels;
  List<Hotel> get searchResults => _searchResults;
  List<Review> get hotelReviews => _hotelReviews;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  Hotel? get selectedHotel => _selectedHotel;

  HotelProvider() {
    _loadSampleData();
  }

  void _loadSampleData() {
    _hotels = [
      Hotel(
        id: '1',
        name: 'فندق الريتز كارلتون',
        description: 'فندق فاخر في قلب المدينة مع إطلالة رائعة على البحر وخدمات عالمية المستوى.',
        location: 'الرياض، المملكة العربية السعودية',
        rating: 4.8,
        reviewCount: 1250,
        pricePerNight: 850.0,
        images: [
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800',
          'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800',
          'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800',
        ],
        amenities: ['واي فاي مجاني', 'مسبح', 'سبا', 'مطعم', 'خدمة الغرف', 'موقف سيارات'],
        latitude: 24.7136,
        longitude: 46.6753,
        isFeatured: true,
      ),
      Hotel(
        id: '2',
        name: 'فندق فورسيزونز',
        description: 'تجربة إقامة استثنائية مع أفضل الخدمات والمرافق الحديثة.',
        location: 'جدة، المملكة العربية السعودية',
        rating: 4.7,
        reviewCount: 980,
        pricePerNight: 720.0,
        images: [
          'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=800',
          'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800',
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800',
        ],
        amenities: ['واي فاي مجاني', 'مسبح', 'نادي رياضي', 'مطعم', 'خدمة الغرف'],
        latitude: 21.3891,
        longitude: 39.8579,
        isFeatured: true,
      ),
      Hotel(
        id: '3',
        name: 'فندق هيلتون',
        description: 'فندق عصري مع مرافق متكاملة وموقع مميز في وسط المدينة.',
        location: 'الدمام، المملكة العربية السعودية',
        rating: 4.5,
        reviewCount: 756,
        pricePerNight: 450.0,
        images: [
          'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=800',
          'https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=800',
          'https://images.unsplash.com/photo-1587985064135-0366536eab42?w=800',
        ],
        amenities: ['واي فاي مجاني', 'مسبح', 'مطعم', 'موقف سيارات', 'خدمة الغرف'],
        latitude: 26.4207,
        longitude: 50.0888,
        isFeatured: false,
      ),
      Hotel(
        id: '4',
        name: 'فندق شيراتون',
        description: 'فندق أنيق مع إطلالة بانورامية وخدمات متميزة.',
        location: 'مكة المكرمة، المملكة العربية السعودية',
        rating: 4.6,
        reviewCount: 1100,
        pricePerNight: 680.0,
        images: [
          'https://images.unsplash.com/photo-1455587734955-081b22074882?w=800',
          'https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?w=800',
          'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800',
        ],
        amenities: ['واي فاي مجاني', 'مسبح', 'سبا', 'مطعم', 'خدمة الغرف', 'نادي رياضي'],
        latitude: 21.3891,
        longitude: 39.8579,
        isFeatured: true,
      ),
    ];

    _featuredHotels = _hotels.where((hotel) => hotel.isFeatured).toList();
    _searchResults = _hotels;
    notifyListeners();
  }

  void searchHotels(String query) {
    _searchQuery = query;
    if (query.isEmpty) {
      _searchResults = _hotels;
    } else {
      _searchResults = _hotels.where((hotel) {
        return hotel.name.toLowerCase().contains(query.toLowerCase()) ||
               hotel.location.toLowerCase().contains(query.toLowerCase()) ||
               hotel.description.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }
    notifyListeners();
  }

  void selectHotel(Hotel hotel) {
    _selectedHotel = hotel;
    notifyListeners();
  }

  void clearSelection() {
    _selectedHotel = null;
    notifyListeners();
  }

  Future<void> refreshHotels() async {
    _isLoading = true;
    notifyListeners();

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    _loadSampleData();
    _isLoading = false;
    notifyListeners();
  }

  List<Hotel> getHotelsByLocation(String location) {
    return _hotels.where((hotel) =>
      hotel.location.toLowerCase().contains(location.toLowerCase())
    ).toList();
  }

  List<Hotel> getHotelsByPriceRange(double minPrice, double maxPrice) {
    return _hotels.where((hotel) =>
      hotel.pricePerNight >= minPrice && hotel.pricePerNight <= maxPrice
    ).toList();
  }

  List<Hotel> getHotelsByRating(double minRating) {
    return _hotels.where((hotel) => hotel.rating >= minRating).toList();
  }

  Hotel? getHotelById(String id) {
    try {
      return _hotels.firstWhere((hotel) => hotel.id == id);
    } catch (e) {
      return null;
    }
  }
}
