import 'package:flutter/material.dart';
import '../models/booking.dart';
import '../models/hotel.dart';

class BookingProvider extends ChangeNotifier {
  List<Booking> _bookings = [];
  bool _isLoading = false;
  DateTime? _checkInDate;
  DateTime? _checkOutDate;
  int _numberOfRooms = 1;
  int _numberOfGuests = 1;
  String? _specialRequests;

  // Getters
  List<Booking> get bookings => _bookings;
  bool get isLoading => _isLoading;
  DateTime? get checkInDate => _checkInDate;
  DateTime? get checkOutDate => _checkOutDate;
  int get numberOfRooms => _numberOfRooms;
  int get numberOfGuests => _numberOfGuests;
  String? get specialRequests => _specialRequests;

  // Filtered bookings
  List<Booking> get upcomingBookings => _bookings
      .where((booking) => 
          booking.status == BookingStatus.confirmed &&
          booking.checkInDate.isAfter(DateTime.now()))
      .toList();

  List<Booking> get pastBookings => _bookings
      .where((booking) => 
          booking.status == BookingStatus.completed ||
          booking.checkOutDate.isBefore(DateTime.now()))
      .toList();

  List<Booking> get cancelledBookings => _bookings
      .where((booking) => booking.status == BookingStatus.cancelled)
      .toList();

  BookingProvider() {
    _loadSampleBookings();
  }

  void _loadSampleBookings() {
    // Sample bookings for demonstration
    _bookings = [];
    notifyListeners();
  }

  // Booking form methods
  void setCheckInDate(DateTime date) {
    _checkInDate = date;
    notifyListeners();
  }

  void setCheckOutDate(DateTime date) {
    _checkOutDate = date;
    notifyListeners();
  }

  void setNumberOfRooms(int rooms) {
    _numberOfRooms = rooms;
    notifyListeners();
  }

  void setNumberOfGuests(int guests) {
    _numberOfGuests = guests;
    notifyListeners();
  }

  void setSpecialRequests(String? requests) {
    _specialRequests = requests;
    notifyListeners();
  }

  // Validation
  bool get isBookingFormValid {
    return _checkInDate != null &&
           _checkOutDate != null &&
           _checkInDate!.isBefore(_checkOutDate!) &&
           _numberOfRooms > 0 &&
           _numberOfGuests > 0;
  }

  int get numberOfNights {
    if (_checkInDate == null || _checkOutDate == null) return 0;
    return _checkOutDate!.difference(_checkInDate!).inDays;
  }

  double calculateTotalPrice(Hotel hotel) {
    if (!isBookingFormValid) return 0.0;
    return hotel.pricePerNight * numberOfNights * _numberOfRooms;
  }

  // Booking operations
  Future<bool> createBooking(Hotel hotel, String userId) async {
    if (!isBookingFormValid) return false;

    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      final booking = Booking(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        hotel: hotel,
        checkInDate: _checkInDate!,
        checkOutDate: _checkOutDate!,
        numberOfRooms: _numberOfRooms,
        numberOfGuests: _numberOfGuests,
        totalPrice: calculateTotalPrice(hotel),
        status: BookingStatus.confirmed,
        bookingDate: DateTime.now(),
        specialRequests: _specialRequests,
      );

      _bookings.add(booking);
      _clearBookingForm();
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> cancelBooking(String bookingId) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final bookingIndex = _bookings.indexWhere((b) => b.id == bookingId);
      if (bookingIndex != -1) {
        _bookings[bookingIndex] = _bookings[bookingIndex].copyWith(
          status: BookingStatus.cancelled,
        );
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  void _clearBookingForm() {
    _checkInDate = null;
    _checkOutDate = null;
    _numberOfRooms = 1;
    _numberOfGuests = 1;
    _specialRequests = null;
  }

  Future<void> refreshBookings() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      _loadSampleBookings();
    } catch (e) {
      // Handle error
    }

    _isLoading = false;
    notifyListeners();
  }

  Booking? getBookingById(String id) {
    try {
      return _bookings.firstWhere((booking) => booking.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Booking> getBookingsByStatus(BookingStatus status) {
    return _bookings.where((booking) => booking.status == status).toList();
  }
}
