import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import '../models/hotel.dart';
import '../providers/booking_provider.dart';
import '../providers/auth_provider.dart';
import '../utils/constants.dart';
import '../widgets/date_picker_widget.dart';
import '../widgets/guest_selector_widget.dart';

class BookingScreen extends StatefulWidget {
  final Hotel hotel;

  const BookingScreen({
    super.key,
    required this.hotel,
  });

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  final _specialRequestsController = TextEditingController();

  @override
  void dispose() {
    _specialRequestsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'حجز الفندق',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: Consumer<BookingProvider>(
        builder: (context, bookingProvider, child) {
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConstants.mediumSpacing),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHotelSummary(),
                      const SizedBox(height: AppConstants.largeSpacing),
                      _buildDateSelection(bookingProvider),
                      const SizedBox(height: AppConstants.largeSpacing),
                      _buildGuestSelection(bookingProvider),
                      const SizedBox(height: AppConstants.largeSpacing),
                      _buildSpecialRequests(),
                      const SizedBox(height: AppConstants.largeSpacing),
                      _buildPriceSummary(bookingProvider),
                    ],
                  ),
                ),
              ),
              _buildBookingButton(bookingProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHotelSummary() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.smallRadius),
            child: Image.network(
              widget.hotel.images.isNotEmpty ? widget.hotel.images.first : '',
              width: 80,
              height: 80,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: 80,
                height: 80,
                color: Colors.grey[300],
                child: const Icon(Icons.image),
              ),
            ),
          ),
          const SizedBox(width: AppConstants.mediumSpacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.hotel.name,
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppConstants.smallSpacing),
                Row(
                  children: [
                    const Icon(
                      Icons.location_on,
                      size: 14,
                      color: AppColors.grey,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        widget.hotel.location,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: AppColors.grey,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.smallSpacing),
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      size: 16,
                      color: AppColors.starColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${widget.hotel.rating}',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelection(BookingProvider bookingProvider) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تواريخ الإقامة',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          Row(
            children: [
              Expanded(
                child: DatePickerWidget(
                  label: 'تاريخ الوصول',
                  selectedDate: bookingProvider.checkInDate,
                  onDateSelected: (date) {
                    bookingProvider.setCheckInDate(date);
                  },
                ),
              ),
              const SizedBox(width: AppConstants.mediumSpacing),
              Expanded(
                child: DatePickerWidget(
                  label: 'تاريخ المغادرة',
                  selectedDate: bookingProvider.checkOutDate,
                  onDateSelected: (date) {
                    bookingProvider.setCheckOutDate(date);
                  },
                ),
              ),
            ],
          ),
          if (bookingProvider.numberOfNights > 0) ...[
            const SizedBox(height: AppConstants.smallSpacing),
            Container(
              padding: const EdgeInsets.all(AppConstants.smallSpacing),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConstants.smallRadius),
              ),
              child: Text(
                'عدد الليالي: ${bookingProvider.numberOfNights}',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.primaryBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGuestSelection(BookingProvider bookingProvider) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الغرف والضيوف',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          GuestSelectorWidget(
            label: 'عدد الغرف',
            value: bookingProvider.numberOfRooms,
            onChanged: (value) {
              bookingProvider.setNumberOfRooms(value);
            },
            icon: Icons.bed,
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          GuestSelectorWidget(
            label: 'عدد الضيوف',
            value: bookingProvider.numberOfGuests,
            onChanged: (value) {
              bookingProvider.setNumberOfGuests(value);
            },
            icon: Icons.group,
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialRequests() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طلبات خاصة (اختياري)',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          TextField(
            controller: _specialRequestsController,
            maxLines: 3,
            textAlign: TextAlign.right,
            style: GoogleFonts.cairo(),
            decoration: InputDecoration(
              hintText: 'أضف أي طلبات خاصة هنا...',
              hintStyle: GoogleFonts.cairo(
                color: Colors.grey[500],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.smallRadius),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.smallRadius),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.smallRadius),
                borderSide: const BorderSide(color: AppColors.primaryBlue),
              ),
            ),
            onChanged: (value) {
              context.read<BookingProvider>().setSpecialRequests(value);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSummary(BookingProvider bookingProvider) {
    final totalPrice = bookingProvider.calculateTotalPrice(widget.hotel);

    return Container(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص الأسعار',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.mediumSpacing),
          if (bookingProvider.isBookingFormValid) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${widget.hotel.pricePerNight.toInt()} ريال × ${bookingProvider.numberOfNights} ليلة × ${bookingProvider.numberOfRooms} غرفة',
                  style: GoogleFonts.cairo(fontSize: 14),
                ),
                Text(
                  '${totalPrice.toInt()} ريال',
                  style: GoogleFonts.cairo(fontSize: 14),
                ),
              ],
            ),
            const Divider(height: AppConstants.largeSpacing),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المجموع الكلي',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${totalPrice.toInt()} ريال',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryBlue,
                  ),
                ),
              ],
            ),
          ] else ...[
            Text(
              'يرجى اختيار التواريخ لعرض الأسعار',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.grey,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBookingButton(BookingProvider bookingProvider) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: bookingProvider.isBookingFormValid && !bookingProvider.isLoading
              ? () => _confirmBooking(bookingProvider)
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryBlue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: AppConstants.mediumSpacing),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
            ),
          ),
          child: bookingProvider.isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : Text(
                  'تأكيد الحجز',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
        ),
      ),
    );
  }

  void _confirmBooking(BookingProvider bookingProvider) async {
    final authProvider = context.read<AuthProvider>();

    if (!authProvider.isLoggedIn) {
      // Navigate to login screen
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'يرجى تسجيل الدخول أولاً',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
      return;
    }

    final success = await bookingProvider.createBooking(
      widget.hotel,
      authProvider.currentUser!.id,
    );

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تأكيد الحجز بنجاح!',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: AppColors.success,
          ),
        );
        Navigator.pop(context);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء الحجز. يرجى المحاولة مرة أخرى.',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
