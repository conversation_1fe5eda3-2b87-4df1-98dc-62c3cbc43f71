import 'package:flutter/material.dart';

class AppIcons {
  // Navigation
  static const IconData home = Icons.home;
  static const IconData calendar = Icons.calendar_today;
  static const IconData user = Icons.person;
  static const IconData search = Icons.search;
  static const IconData filter = Icons.tune;
  static const IconData close = Icons.close;
  static const IconData back = Icons.arrow_back;
  static const IconData forward = Icons.arrow_forward;
  static const IconData arrowRight = Icons.arrow_forward_ios;
  static const IconData arrowLeft = Icons.arrow_back_ios;
  static const IconData chevronLeft = Icons.chevron_left;
  static const IconData chevronRight = Icons.chevron_right;
  
  // Hotel & Booking
  static const IconData hotel = Icons.hotel;
  static const IconData bed = Icons.bed;
  static const IconData mapPin = Icons.location_on;
  static const IconData star = Icons.star;
  static const IconData starOutline = Icons.star_border;
  static const IconData camera = Icons.camera_alt;
  static const IconData image = Icons.image;
  static const IconData users = Icons.group;
  static const IconData moon = Icons.nightlight_round;
  static const IconData sun = Icons.wb_sunny;
  static const IconData map = Icons.map;
  
  // Actions
  static const IconData heart = Icons.favorite_border;
  static const IconData heartFilled = Icons.favorite;
  static const IconData share = Icons.share;
  static const IconData refresh = Icons.refresh;
  static const IconData add = Icons.add;
  static const IconData remove = Icons.remove;
  static const IconData plus = Icons.add;
  static const IconData minus = Icons.remove;
  static const IconData edit = Icons.edit;
  static const IconData delete = Icons.delete;
  
  // Authentication
  static const IconData mail = Icons.email;
  static const IconData lock = Icons.lock;
  static const IconData eye = Icons.visibility;
  static const IconData eyeOff = Icons.visibility_off;
  static const IconData logIn = Icons.login;
  static const IconData logOut = Icons.logout;
  static const IconData google = Icons.g_mobiledata;
  
  // Settings
  static const IconData settings = Icons.settings;
  static const IconData bell = Icons.notifications;
  static const IconData globe = Icons.language;
  static const IconData helpCircle = Icons.help_outline;
  static const IconData fileText = Icons.description;
  static const IconData shield = Icons.security;
  static const IconData sliders = Icons.tune;
  
  // Status
  static const IconData check = Icons.check;
  static const IconData x = Icons.close;
  static const IconData info = Icons.info_outline;
  static const IconData warning = Icons.warning_amber_outlined;
  static const IconData error = Icons.error_outline;
  static const IconData success = Icons.check_circle_outline;
  
  // Misc
  static const IconData phone = Icons.phone;
  static const IconData wifi = Icons.wifi;
  static const IconData car = Icons.directions_car;
  static const IconData restaurant = Icons.restaurant;
  static const IconData pool = Icons.pool;
  static const IconData spa = Icons.spa;
  static const IconData gym = Icons.fitness_center;
  static const IconData parking = Icons.local_parking;
  static const IconData roomService = Icons.room_service;
  static const IconData searchX = Icons.search_off;
}
