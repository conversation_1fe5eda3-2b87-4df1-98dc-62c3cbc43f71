import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../utils/constants.dart';

class AuthProvider extends ChangeNotifier {
  User? _currentUser;
  bool _isLoggedIn = false;
  bool _isLoading = false;

  // Getters
  User? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;

  AuthProvider() {
    _loadUserData();
  }

  void _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();
    _isLoggedIn = prefs.getBool(AppConstants.isLoggedInKey) ?? false;
    
    if (_isLoggedIn) {
      final userData = prefs.getString(AppConstants.userKey);
      if (userData != null) {
        // In a real app, you would parse the JSON here
        _currentUser = User(
          id: '1',
          name: 'مستخدم تجريبي',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
        );
      }
    }
    notifyListeners();
  }

  Future<bool> login(String email, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Simple validation for demo
      if (email.isNotEmpty && password.length >= 6) {
        _currentUser = User(
          id: '1',
          name: 'مستخدم تجريبي',
          email: email,
          isEmailVerified: true,
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );

        _isLoggedIn = true;
        await _saveUserData();
        
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> register(String name, String email, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Simple validation for demo
      if (name.isNotEmpty && email.isNotEmpty && password.length >= 6) {
        _currentUser = User(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          email: email,
          isEmailVerified: false,
          createdAt: DateTime.now(),
        );

        _isLoggedIn = true;
        await _saveUserData();
        
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> loginWithGoogle() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate Google login
      await Future.delayed(const Duration(seconds: 2));

      _currentUser = User(
        id: 'google_user_1',
        name: 'مستخدم Google',
        email: '<EMAIL>',
        isEmailVerified: true,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );

      _isLoggedIn = true;
      await _saveUserData();
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    _isLoggedIn = false;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userKey);
    await prefs.setBool(AppConstants.isLoggedInKey, false);
    
    notifyListeners();
  }

  Future<bool> updateProfile({
    String? name,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? nationality,
  }) async {
    if (_currentUser == null) return false;

    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      _currentUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        phoneNumber: phoneNumber ?? _currentUser!.phoneNumber,
        dateOfBirth: dateOfBirth ?? _currentUser!.dateOfBirth,
        nationality: nationality ?? _currentUser!.nationality,
      );

      await _saveUserData();
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> changePassword(String currentPassword, String newPassword) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // In a real app, you would validate the current password
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> _saveUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.isLoggedInKey, _isLoggedIn);
    
    if (_currentUser != null) {
      // In a real app, you would save the user data as JSON
      await prefs.setString(AppConstants.userKey, _currentUser!.toJson().toString());
    }
  }

  bool validateEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool validatePassword(String password) {
    return password.length >= 6;
  }
}
