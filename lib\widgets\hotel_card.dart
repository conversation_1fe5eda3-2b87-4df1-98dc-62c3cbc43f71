import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import '../models/hotel.dart';
import '../utils/constants.dart';

class HotelCard extends StatelessWidget {
  final Hotel hotel;
  final VoidCallback onTap;
  final bool isCompact;

  const HotelCard({
    super.key,
    required this.hotel,
    required this.onTap,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
          boxShadow: AppShadows.cardShadow,
          color: Theme.of(context).cardColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildImageSection(),
            _buildContentSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(AppConstants.mediumRadius),
            topRight: Radius.circular(AppConstants.mediumRadius),
          ),
          child: CachedNetworkImage(
            imageUrl: hotel.images.isNotEmpty ? hotel.images.first : '',
            height: isCompact ? 120 : 200,
            width: double.infinity,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              height: isCompact ? 120 : 200,
              color: Colors.grey[300],
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              height: isCompact ? 120 : 200,
              color: Colors.grey[300],
              child: const Icon(
                Icons.image,
                size: 48,
                color: Colors.grey,
              ),
            ),
          ),
        ),
        if (hotel.isFeatured)
          Positioned(
            top: AppConstants.smallSpacing,
            right: AppConstants.smallSpacing,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.smallSpacing,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: AppColors.secondaryOrange,
                borderRadius: BorderRadius.circular(AppConstants.smallRadius),
              ),
              child: Text(
                'مميز',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        Positioned(
          bottom: AppConstants.smallSpacing,
          left: AppConstants.smallSpacing,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.smallSpacing,
              vertical: 4,
            ),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(AppConstants.smallRadius),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.camera_alt,
                  size: 12,
                  color: Colors.white,
                ),
                const SizedBox(width: 4),
                Text(
                  '${hotel.images.length}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContentSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            hotel.name,
            style: GoogleFonts.cairo(
              fontSize: isCompact ? 16 : 18,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          Row(
            children: [
              const Icon(
                Icons.location_on,
                size: 14,
                color: AppColors.grey,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  hotel.location,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: AppColors.grey,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallSpacing),
          Row(
            children: [
              RatingBarIndicator(
                rating: hotel.rating,
                itemBuilder: (context, index) => const Icon(
                  Icons.star,
                  color: AppColors.starColor,
                ),
                itemCount: 5,
                itemSize: 16,
                direction: Axis.horizontal,
              ),
              const SizedBox(width: AppConstants.smallSpacing),
              Text(
                '${hotel.rating}',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                '(${hotel.reviewCount} تقييم)',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: AppColors.grey,
                ),
              ),
            ],
          ),
          if (!isCompact) ...[
            const SizedBox(height: AppConstants.smallSpacing),
            Text(
              hotel.description,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.grey,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          const SizedBox(height: AppConstants.mediumSpacing),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ابتداءً من',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: AppColors.grey,
                    ),
                  ),
                  Row(
                    children: [
                      Text(
                        '${hotel.pricePerNight.toInt()}',
                        style: GoogleFonts.cairo(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'دج/ليلة',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: AppColors.grey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.mediumSpacing,
                  vertical: AppConstants.smallSpacing,
                ),
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  borderRadius: BorderRadius.circular(AppConstants.smallRadius),
                ),
                child: Text(
                  'احجز الآن',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
