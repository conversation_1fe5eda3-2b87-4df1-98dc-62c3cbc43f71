import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/app_icons.dart';
import '../providers/hotel_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import '../utils/constants.dart';
import '../widgets/hotel_card.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/featured_hotels_section.dart';
import 'hotel_details_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () => context.read<HotelProvider>().refreshHotels(),
          child: CustomScrollView(
            slivers: [
              _buildAppBar(context),
              _buildSearchSection(context),
              _buildFeaturedSection(context),
              _buildHotelsSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: context.watch<ThemeProvider>().isDarkMode
                ? AppColors.darkCardGradient
                : AppColors.primaryGradient,
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.mediumSpacing),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مرحباً',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            color: Colors.white70,
                          ),
                        ),
                        Consumer<AuthProvider>(
                          builder: (context, auth, child) {
                            return Text(
                              auth.currentUser?.name ?? 'ضيف',
                              style: GoogleFonts.cairo(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            context.read<ThemeProvider>().toggleTheme();
                          },
                          icon: Icon(
                            context.watch<ThemeProvider>().isDarkMode
                                ? AppIcons.sun
                                : AppIcons.moon,
                            color: Colors.white,
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            // Navigate to notifications
                          },
                          icon: const Icon(
                            AppIcons.bell,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchSection(BuildContext context) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.mediumSpacing),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ابحث عن فندقك المثالي',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.mediumSpacing),
            SearchBarWidget(
              controller: _searchController,
              onChanged: (query) {
                context.read<HotelProvider>().searchHotels(query);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedSection(BuildContext context) {
    return SliverToBoxAdapter(
      child: Consumer<HotelProvider>(
        builder: (context, hotelProvider, child) {
          if (hotelProvider.featuredHotels.isEmpty) {
            return const SizedBox.shrink();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.mediumSpacing,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'العروض المميزة',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        // Navigate to all featured hotels
                      },
                      child: Text(
                        'عرض الكل',
                        style: GoogleFonts.cairo(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              FeaturedHotelsSection(
                hotels: hotelProvider.featuredHotels,
                onHotelTap: (hotel) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => HotelDetailsScreen(hotel: hotel),
                    ),
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHotelsSection(BuildContext context) {
    return Consumer<HotelProvider>(
      builder: (context, hotelProvider, child) {
        if (hotelProvider.isLoading) {
          return const SliverToBoxAdapter(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(AppConstants.largeSpacing),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        final hotels = hotelProvider.searchQuery.isEmpty
            ? hotelProvider.hotels
            : hotelProvider.searchResults;

        if (hotels.isEmpty) {
          return SliverToBoxAdapter(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.largeSpacing),
                child: Column(
                  children: [
                    Icon(
                      AppIcons.searchX,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: AppConstants.mediumSpacing),
                    Text(
                      'لم يتم العثور على فنادق',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: AppConstants.smallSpacing),
                    Text(
                      'جرب البحث بكلمات مختلفة',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        return SliverPadding(
          padding: const EdgeInsets.all(AppConstants.mediumSpacing),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final hotel = hotels[index];
                return Padding(
                  padding: const EdgeInsets.only(
                    bottom: AppConstants.mediumSpacing,
                  ),
                  child: HotelCard(
                    hotel: hotel,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => HotelDetailsScreen(hotel: hotel),
                        ),
                      );
                    },
                  ),
                );
              },
              childCount: hotels.length,
            ),
          ),
        );
      },
    );
  }
}
