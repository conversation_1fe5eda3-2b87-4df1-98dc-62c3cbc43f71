import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/constants.dart';

class GuestSelectorWidget extends StatelessWidget {
  final String label;
  final int value;
  final Function(int) onChanged;
  final IconData icon;
  final int minValue;
  final int maxValue;

  const GuestSelectorWidget({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
    required this.icon,
    this.minValue = 1,
    this.maxValue = 10,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(AppConstants.smallRadius),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 24,
            color: AppColors.primaryBlue,
          ),
          const SizedBox(width: AppConstants.mediumSpacing),
          Expanded(
            child: Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Row(
            children: [
              _buildButton(
                icon: Icons.remove,
                onPressed: value > minValue ? () => onChanged(value - 1) : null,
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: AppConstants.mediumSpacing),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.mediumSpacing,
                  vertical: AppConstants.smallSpacing,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppConstants.smallRadius),
                ),
                child: Text(
                  value.toString(),
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryBlue,
                  ),
                ),
              ),
              _buildButton(
                icon: Icons.add,
                onPressed: value < maxValue ? () => onChanged(value + 1) : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildButton({
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: onPressed != null ? AppColors.primaryBlue : Colors.grey[300],
        borderRadius: BorderRadius.circular(AppConstants.smallRadius),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: 18,
          color: onPressed != null ? Colors.white : Colors.grey[500],
        ),
        constraints: const BoxConstraints(
          minWidth: 36,
          minHeight: 36,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }
}
