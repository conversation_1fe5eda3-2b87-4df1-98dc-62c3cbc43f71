import 'hotel.dart';

enum BookingStatus {
  pending,
  confirmed,
  cancelled,
  completed,
}

class Booking {
  final String id;
  final String userId;
  final Hotel hotel;
  final DateTime checkInDate;
  final DateTime checkOutDate;
  final int numberOfRooms;
  final int numberOfGuests;
  final double totalPrice;
  final BookingStatus status;
  final DateTime bookingDate;
  final String? specialRequests;

  Booking({
    required this.id,
    required this.userId,
    required this.hotel,
    required this.checkInDate,
    required this.checkOutDate,
    required this.numberOfRooms,
    required this.numberOfGuests,
    required this.totalPrice,
    required this.status,
    required this.bookingDate,
    this.specialRequests,
  });

  int get numberOfNights {
    return checkOutDate.difference(checkInDate).inDays;
  }

  factory Booking.fromJson(Map<String, dynamic> json) {
    return Booking(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      hotel: Hotel.fromJson(json['hotel'] ?? {}),
      checkInDate: DateTime.parse(json['checkInDate'] ?? DateTime.now().toIso8601String()),
      checkOutDate: DateTime.parse(json['checkOutDate'] ?? DateTime.now().toIso8601String()),
      numberOfRooms: json['numberOfRooms'] ?? 1,
      numberOfGuests: json['numberOfGuests'] ?? 1,
      totalPrice: (json['totalPrice'] ?? 0.0).toDouble(),
      status: BookingStatus.values.firstWhere(
        (e) => e.toString() == 'BookingStatus.${json['status']}',
        orElse: () => BookingStatus.pending,
      ),
      bookingDate: DateTime.parse(json['bookingDate'] ?? DateTime.now().toIso8601String()),
      specialRequests: json['specialRequests'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'hotel': hotel.toJson(),
      'checkInDate': checkInDate.toIso8601String(),
      'checkOutDate': checkOutDate.toIso8601String(),
      'numberOfRooms': numberOfRooms,
      'numberOfGuests': numberOfGuests,
      'totalPrice': totalPrice,
      'status': status.toString().split('.').last,
      'bookingDate': bookingDate.toIso8601String(),
      'specialRequests': specialRequests,
    };
  }

  Booking copyWith({
    String? id,
    String? userId,
    Hotel? hotel,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? numberOfRooms,
    int? numberOfGuests,
    double? totalPrice,
    BookingStatus? status,
    DateTime? bookingDate,
    String? specialRequests,
  }) {
    return Booking(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      hotel: hotel ?? this.hotel,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      numberOfRooms: numberOfRooms ?? this.numberOfRooms,
      numberOfGuests: numberOfGuests ?? this.numberOfGuests,
      totalPrice: totalPrice ?? this.totalPrice,
      status: status ?? this.status,
      bookingDate: bookingDate ?? this.bookingDate,
      specialRequests: specialRequests ?? this.specialRequests,
    );
  }
}
